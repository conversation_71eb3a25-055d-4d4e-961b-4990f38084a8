# EPD Manager App - 模板管理功能實現

## 概述

已成功為 EPD Manager App 添加了完整的模板管理功能，包括模板列表顯示、縮圖預覽、篩選功能等。

## 實現的功能

### 1. 模板數據結構
- 在 `src/types/index.ts` 中添加了完整的模板相關類型定義
- 支持模板元素、模板類型、篩選器等類型
- 包含預覽圖、門店關聯、系統模板標識等字段

### 2. API 服務
- 在 `src/services/ApiService.ts` 中添加了模板相關的 API 方法：
  - `getTemplates()` - 獲取模板列表
  - `getTemplate()` - 獲取單個模板詳情
  - `createTemplate()` - 創建新模板
  - `updateTemplate()` - 更新模板
  - `deleteTemplate()` - 刪除模板

### 3. 狀態管理
- 創建了 `src/stores/templateStore.ts` 模板狀態管理 store
- 使用 Zustand 進行狀態管理
- 支持模板的 CRUD 操作
- 集成 WebSocket 實時更新功能
- 提供篩選和搜索功能

### 4. 模板管理頁面
- 創建了 `src/screens/TemplateManagementScreen.tsx` 模板管理頁面
- 功能特點：
  - 網格布局顯示模板卡片
  - 模板縮圖預覽（支持 base64 格式圖片）
  - 搜索功能（按名稱、尺寸、顏色搜索）
  - 篩選標籤（全部模板、系統模板、門店模板）
  - 長按刪除模板功能
  - 下拉刷新支持
  - 實時數據更新

### 5. UI 組件
- 更新了 `src/components/FilterTabs.tsx` 篩選標籤組件
- 修復了接口不匹配問題
- 使用統一的設計系統常量

### 6. 主控制台集成
- 在 `src/screens/MainConsoleScreen.tsx` 中添加了模板管理入口
- 新增模板管理按鈕（🎨 圖標）
- 使用 Modal 方式展示模板管理頁面

### 7. WebSocket 實時更新
- 集成了 WebSocket 服務以支持模板的實時更新
- 當模板在其他地方被修改時，app 會自動更新顯示
- 支持模板的新增、更新、刪除事件

## 頁面功能詳細說明

### 模板卡片顯示
每個模板卡片包含：
- 預覽圖（如果有的話）
- 模板名稱
- 屏幕尺寸和顏色類型
- 模板類型標籤（系統/門店）
- 數據類型標籤（單一/多重）

### 篩選功能
- **全部模板**：顯示所有可用的模板
- **系統模板**：只顯示系統級別的通用模板
- **門店模板**：只顯示當前門店專屬的模板

### 搜索功能
支持按以下條件搜索：
- 模板名稱
- 屏幕尺寸
- 顏色類型

### 操作功能
- **查看**：點擊模板卡片查看詳情
- **刪除**：長按模板卡片可刪除（會有確認對話框）
- **刷新**：下拉刷新獲取最新模板列表

## 技術實現細節

### 狀態管理架構
```typescript
interface TemplateStore {
  // 狀態
  templates: Template[];
  loading: boolean;
  error: string | null;
  filter: TemplateFilter;
  
  // 基本操作
  setTemplates, addTemplate, updateTemplate, removeTemplate;
  
  // 異步操作
  fetchTemplates, createTemplate, deleteTemplate;
  
  // WebSocket 訂閱
  subscribeToTemplateUpdates, unsubscribeFromTemplateUpdates;
  
  // 工具方法
  getFilteredTemplates, getSystemTemplates, getStoreTemplates;
}
```

### API 集成
- 使用統一的 ApiService 進行 HTTP 請求
- 支持門店 ID 參數篩選
- 錯誤處理和重試機制
- 認證 token 自動添加

### WebSocket 集成
- 使用 FrontendWebSocketClient 進行實時通信
- 自動訂閱和取消訂閱模板更新事件
- 事件處理器模式，支持多個監聽器

## 使用方式

1. 在主控制台頁面點擊「模板管理」按鈕
2. 進入模板管理頁面後可以：
   - 瀏覽所有可用模板
   - 使用搜索框搜索特定模板
   - 使用篩選標籤切換不同類型的模板
   - 長按模板卡片刪除不需要的模板
   - 下拉刷新獲取最新數據

## 後續擴展建議

1. **模板詳情頁面**：點擊模板卡片時顯示詳細信息
2. **模板編輯功能**：允許在 app 中編輯模板
3. **模板創建功能**：支持在 app 中創建新模板
4. **模板導入導出**：支持模板的導入和導出
5. **模板預覽優化**：更好的預覽圖生成和顯示
6. **批量操作**：支持批量刪除、複製等操作

## 重要修復：預覽圖顯示問題

### 問題描述
最初實現時發現模板預覽圖無法顯示，經過分析發現是因為服務器端的模板列表 API (`/api/templates`) 只返回模板摘要信息，不包含 `previewImage` 字段。

### 解決方案
修改了 `templateStore.ts` 中的 `fetchTemplates` 方法：

1. **兩步獲取策略**：
   - 首先調用 `/api/templates` 獲取模板摘要列表
   - 然後為每個模板調用 `/api/templates/:id` 獲取完整信息（包括預覽圖）

2. **錯誤處理**：
   - 如果獲取某個模板的詳情失敗，會回退到使用摘要信息
   - 添加了詳細的日誌記錄以便調試

3. **性能考慮**：
   - 使用 `Promise.all` 並行獲取所有模板詳情
   - 避免阻塞用戶界面

### 代碼實現
```typescript
// 首先獲取模板摘要列表
const response = await apiService.getTemplates(storeId);

if (response.success && response.data) {
  const templateSummaries = response.data;

  // 為每個模板獲取完整信息（包括預覽圖）
  const templatesWithPreview = await Promise.all(
    templateSummaries.map(async (summary) => {
      try {
        const detailResponse = await apiService.getTemplate(summary.id, storeId);
        if (detailResponse.success && detailResponse.data) {
          return detailResponse.data; // 包含 previewImage
        } else {
          return summary; // 回退到摘要信息
        }
      } catch (error) {
        return summary; // 錯誤時回退
      }
    })
  );

  set({ templates: templatesWithPreview, loading: false });
}
```

## 注意事項

- 模板管理功能需要服務器端支持相應的 API 端點
- WebSocket 功能需要服務器端支持模板更新事件廣播
- 預覽圖功能依賴於模板的 previewImage 字段（base64 格式）
- 刪除操作會有確認對話框，防止誤操作
- **重要**：由於需要獲取每個模板的詳細信息，初次加載可能會比較慢，建議在服務器端優化模板列表 API 以包含預覽圖
