// EPD Manager App - 模板狀態管理

import { create } from 'zustand';
import { Template, TemplateState, TemplateFilter } from '../types';
import { apiService } from '../services/ApiService';
import { frontendWebSocketClient, TemplateUpdateEventHandler } from '../services/WebSocketService';

interface TemplateStore extends TemplateState {
  // 狀態
  filter: TemplateFilter;
  
  // 動作
  setTemplates: (templates: Template[]) => void;
  addTemplate: (template: Template) => void;
  updateTemplate: (templateId: string, updates: Partial<Template>) => void;
  removeTemplate: (templateId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setFilter: (filter: Partial<TemplateFilter>) => void;
  
  // 異步動作
  fetchTemplates: (storeId?: string) => Promise<void>;
  createTemplate: (templateData: Partial<Template>) => Promise<Template | null>;
  deleteTemplate: (templateId: string) => Promise<boolean>;
  subscribeToTemplateUpdates: (storeId?: string) => void;
  unsubscribeFromTemplateUpdates: (storeId?: string) => void;
  
  // 工具方法
  getFilteredTemplates: () => Template[];
  getSystemTemplates: () => Template[];
  getStoreTemplates: (storeId: string) => Template[];
}

export const useTemplates = create<TemplateStore>((set, get) => ({
  // 初始狀態
  templates: [],
  loading: false,
  error: null,
  filter: {
    type: 'all',
    searchTerm: '',
  },

  // 基本狀態更新
  setTemplates: (templates) => set({ templates }),
  
  addTemplate: (template) => set((state) => ({
    templates: [...state.templates, template],
  })),
  
  updateTemplate: (templateId, updates) => set((state) => ({
    templates: state.templates.map(template =>
      template.id === templateId ? { ...template, ...updates } : template
    ),
  })),
  
  removeTemplate: (templateId) => set((state) => ({
    templates: state.templates.filter(template => template.id !== templateId),
  })),
  
  setLoading: (loading) => set({ loading }),
  
  setError: (error) => set({ error }),
  
  setFilter: (filter) => set((state) => ({
    filter: { ...state.filter, ...filter },
  })),

  // 異步動作
  fetchTemplates: async (storeId) => {
    set({ loading: true, error: null });

    try {
      // 首先獲取模板摘要列表
      const response = await apiService.getTemplates(storeId);

      if (response.success && response.data) {
        const templateSummaries = response.data;

        // 為每個模板獲取完整信息（包括預覽圖）
        console.log(`開始獲取 ${templateSummaries.length} 個模板的詳細信息...`);
        const templatesWithPreview = await Promise.all(
          templateSummaries.map(async (summary) => {
            try {
              const detailResponse = await apiService.getTemplate(summary.id, storeId);
              if (detailResponse.success && detailResponse.data) {
                const template = detailResponse.data;
                console.log(`模板 ${template.name} (${template.id}) 預覽圖:`,
                  template.previewImage ? `有 (${template.previewImage.length} 字符)` : '無');
                return template;
              } else {
                // 如果獲取詳情失敗，返回摘要信息
                console.warn(`獲取模板 ${summary.id} 詳情失敗:`, detailResponse.error);
                return summary;
              }
            } catch (error) {
              console.warn(`獲取模板 ${summary.id} 詳情時發生錯誤:`, error);
              return summary;
            }
          })
        );

        set({ templates: templatesWithPreview, loading: false });
      } else {
        set({ error: response.error || '獲取模板列表失敗', loading: false });
      }
    } catch (error: any) {
      console.error('獲取模板列表失敗:', error);
      set({ error: error.message || '獲取模板列表失敗', loading: false });
    }
  },

  createTemplate: async (templateData) => {
    set({ loading: true, error: null });
    
    try {
      const response = await apiService.createTemplate(templateData);
      
      if (response.success && response.data) {
        const newTemplate = response.data;
        set((state) => ({
          templates: [...state.templates, newTemplate],
          loading: false,
        }));
        return newTemplate;
      } else {
        set({ error: response.error || '創建模板失敗', loading: false });
        return null;
      }
    } catch (error: any) {
      console.error('創建模板失敗:', error);
      set({ error: error.message || '創建模板失敗', loading: false });
      return null;
    }
  },

  deleteTemplate: async (templateId) => {
    set({ loading: true, error: null });
    
    try {
      const response = await apiService.deleteTemplate(templateId);
      
      if (response.success) {
        set((state) => ({
          templates: state.templates.filter(template => template.id !== templateId),
          loading: false,
        }));
        return true;
      } else {
        set({ error: response.error || '刪除模板失敗', loading: false });
        return false;
      }
    } catch (error: any) {
      console.error('刪除模板失敗:', error);
      set({ error: error.message || '刪除模板失敗', loading: false });
      return false;
    }
  },

  // WebSocket 訂閱
  subscribeToTemplateUpdates: (storeId) => {
    if (frontendWebSocketClient.isConnected) {
      frontendWebSocketClient.subscribeTemplateUpdate(storeId || 'all');

      // 監聽模板更新事件
      const handleTemplateUpdate: TemplateUpdateEventHandler = (event) => {
        const { templates: updatedTemplates, updateType } = event;

        if (updateType === 'delete') {
          // 處理刪除
          updatedTemplates.forEach((template: Template) => {
            get().removeTemplate(template.id);
          });
        } else {
          // 處理新增或更新
          updatedTemplates.forEach((template: Template) => {
            const existingIndex = get().templates.findIndex(t => t.id === template.id);
            if (existingIndex >= 0) {
              get().updateTemplate(template.id, template);
            } else {
              get().addTemplate(template);
            }
          });
        }
      };

      // 添加事件監聽器
      frontendWebSocketClient.addTemplateUpdateListener(handleTemplateUpdate);
    }
  },

  unsubscribeFromTemplateUpdates: (storeId) => {
    if (frontendWebSocketClient.isConnected) {
      frontendWebSocketClient.unsubscribeTemplateUpdate(storeId || 'all');
    }
  },

  // 工具方法
  getFilteredTemplates: () => {
    const { templates, filter } = get();
    let filtered = [...templates];

    // 按類型篩選
    if (filter.type === 'system') {
      filtered = filtered.filter(template => template.isSystemTemplate);
    } else if (filter.type === 'store') {
      filtered = filtered.filter(template => !template.isSystemTemplate && template.storeId);
    }

    // 按屏幕尺寸篩選
    if (filter.screenSize) {
      filtered = filtered.filter(template => template.screenSize === filter.screenSize);
    }

    // 按顏色類型篩選
    if (filter.colorType) {
      filtered = filtered.filter(template => template.color === filter.colorType);
    }

    // 按搜索詞篩選
    if (filter.searchTerm) {
      const searchTerm = filter.searchTerm.toLowerCase();
      filtered = filtered.filter(template =>
        template.name.toLowerCase().includes(searchTerm) ||
        template.screenSize.toLowerCase().includes(searchTerm) ||
        template.color.toLowerCase().includes(searchTerm)
      );
    }

    return filtered;
  },

  getSystemTemplates: () => {
    return get().templates.filter(template => template.isSystemTemplate);
  },

  getStoreTemplates: (storeId) => {
    return get().templates.filter(template => 
      !template.isSystemTemplate && template.storeId === storeId
    );
  },
}));
