// EPD Manager App - 模板管理頁面

import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTemplates } from '../stores/templateStore';
import { useStores } from '../stores/storeStore';
import { FilterTabs } from '../components/FilterTabs';
import { COLORS, SIZES } from '../utils/constants';
import { Template, TemplateFilter } from '../types';

const { width: screenWidth } = Dimensions.get('window');
const cardWidth = (screenWidth - SIZES.SPACING_MD * 3) / 2; // 兩列布局

export const TemplateManagementScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const [searchText, setSearchText] = useState('');
  
  const { selectedStore } = useStores();
  const {
    templates,
    loading,
    error,
    filter,
    fetchTemplates,
    setFilter,
    deleteTemplate,
    getFilteredTemplates,
    subscribeToTemplateUpdates,
    unsubscribeFromTemplateUpdates,
  } = useTemplates();

  useEffect(() => {
    // 頁面加載時獲取模板列表
    if (selectedStore) {
      loadTemplates();
      // 訂閱模板更新
      subscribeToTemplateUpdates(selectedStore.id);
    }

    return () => {
      // 清理訂閱
      if (selectedStore) {
        unsubscribeFromTemplateUpdates(selectedStore.id);
      }
    };
  }, [selectedStore]);

  useEffect(() => {
    // 搜索文字變化時更新篩選器
    setFilter({ searchTerm: searchText });
  }, [searchText]);

  const loadTemplates = async () => {
    if (!selectedStore) return;
    await fetchTemplates(selectedStore.id);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTemplates();
    setRefreshing(false);
  };

  const handleDeleteTemplate = (template: Template) => {
    Alert.alert(
      '確認刪除',
      `確定要刪除模板「${template.name}」嗎？`,
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '刪除',
          style: 'destructive',
          onPress: async () => {
            const success = await deleteTemplate(template.id);
            if (success) {
              Alert.alert('成功', '模板已刪除');
            }
          },
        },
      ]
    );
  };

  const handleFilterChange = (filterType: string) => {
    setFilter({ type: filterType as TemplateFilter['type'] });
  };

  const filteredTemplates = getFilteredTemplates();

  const filterOptions = [
    { key: 'all', label: '全部模板', count: templates.length },
    { key: 'system', label: '系統模板', count: templates.filter(t => t.isSystemTemplate).length },
    { key: 'store', label: '門店模板', count: templates.filter(t => !t.isSystemTemplate && t.storeId).length },
  ];

  const renderTemplateCard = (template: Template) => (
    <TouchableOpacity
      key={template.id}
      style={styles.templateCard}
      onLongPress={() => handleDeleteTemplate(template)}
    >
      {/* 預覽圖 */}
      <View style={styles.previewContainer}>
        {template.previewImage ? (
          <Image
            source={{ uri: template.previewImage }}
            style={styles.previewImage}
            resizeMode="contain"
            onLoad={() => console.log(`模板 ${template.name} 預覽圖加載成功`)}
            onError={(error) => console.error(`模板 ${template.name} 預覽圖加載失敗:`, error)}
          />
        ) : (
          <View style={styles.noPreviewContainer}>
            <Text style={styles.noPreviewText}>無預覽圖</Text>
          </View>
        )}
      </View>

      {/* 模板信息 */}
      <View style={styles.templateInfo}>
        <Text style={styles.templateName} numberOfLines={2}>
          {template.name}
        </Text>
        
        <View style={styles.templateMeta}>
          <Text style={styles.templateMetaText}>
            {template.screenSize}
          </Text>
          <Text style={styles.templateMetaText}>
            {template.color}
          </Text>
        </View>

        {/* 模板類型標籤 */}
        <View style={styles.templateTags}>
          {template.isSystemTemplate ? (
            <View style={[styles.tag, styles.systemTag]}>
              <Text style={styles.systemTagText}>系統</Text>
            </View>
          ) : (
            <View style={[styles.tag, styles.storeTag]}>
              <Text style={styles.storeTagText}>門店</Text>
            </View>
          )}
          
          <View style={[styles.tag, styles.typeTag]}>
            <Text style={styles.typeTagText}>
              {template.type === 'Single data template' ? '單一' : '多重'}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* 頭部 */}
      <View style={styles.header}>
        <Text style={styles.title}>模板管理</Text>
        <Text style={styles.subtitle}>
          {selectedStore?.name || '未選擇門店'}
        </Text>
      </View>

      {/* 搜索框 */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="搜索模板名稱、尺寸或顏色..."
          value={searchText}
          onChangeText={setSearchText}
          placeholderTextColor={COLORS.TEXT_SECONDARY}
        />
      </View>

      {/* 篩選標籤 */}
      <FilterTabs
        options={filterOptions}
        selectedKey={filter.type || 'all'}
        onSelect={handleFilterChange}
        style={styles.filterTabs}
      />

      {/* 模板列表 */}
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.PRIMARY]}
          />
        }
      >
        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {loading && !refreshing && (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>載入中...</Text>
          </View>
        )}

        {!loading && !error && filteredTemplates.length === 0 && (
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>
              {searchText ? '沒有找到符合條件的模板' : '暫無模板'}
            </Text>
          </View>
        )}

        {!loading && !error && filteredTemplates.length > 0 && (
          <View style={styles.templatesGrid}>
            {filteredTemplates.map(renderTemplateCard)}
          </View>
        )}

        {/* 底部間距 */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  header: {
    padding: SIZES.SPACING_MD,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.TEXT_DISABLED,
  },
  title: {
    fontSize: SIZES.FONT_SIZE_XL,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  subtitle: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
    marginTop: SIZES.SPACING_XS,
  },
  searchContainer: {
    padding: SIZES.SPACING_MD,
  },
  searchInput: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    paddingHorizontal: SIZES.SPACING_MD,
    paddingVertical: SIZES.SPACING_SM,
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_PRIMARY,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
  },
  filterTabs: {
    marginHorizontal: SIZES.SPACING_MD,
    marginBottom: SIZES.SPACING_SM,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: SIZES.SPACING_MD,
  },
  templatesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  templateCard: {
    width: cardWidth,
    backgroundColor: COLORS.SURFACE,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    marginBottom: SIZES.SPACING_MD,
    borderWidth: 1,
    borderColor: COLORS.TEXT_DISABLED,
    overflow: 'hidden',
  },
  previewContainer: {
    height: 120,
    backgroundColor: COLORS.BACKGROUND,
  },
  previewImage: {
    width: '100%',
    height: '100%',
  },
  noPreviewContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noPreviewText: {
    fontSize: SIZES.FONT_SIZE_SM,
    color: COLORS.TEXT_SECONDARY,
  },
  templateInfo: {
    padding: SIZES.SPACING_SM,
  },
  templateName: {
    fontSize: SIZES.FONT_SIZE_MD,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SIZES.SPACING_XS,
  },
  templateMeta: {
    marginBottom: SIZES.SPACING_XS,
  },
  templateMetaText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.TEXT_SECONDARY,
  },
  templateTags: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SIZES.SPACING_XS,
  },
  tag: {
    paddingHorizontal: SIZES.SPACING_XS,
    paddingVertical: 2,
    borderRadius: SIZES.BORDER_RADIUS_SM,
  },
  systemTag: {
    backgroundColor: COLORS.INFO,
  },
  systemTagText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.SURFACE,
    fontWeight: '600',
  },
  storeTag: {
    backgroundColor: COLORS.SUCCESS,
  },
  storeTagText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.SURFACE,
    fontWeight: '600',
  },
  typeTag: {
    backgroundColor: COLORS.TEXT_DISABLED,
  },
  typeTagText: {
    fontSize: SIZES.FONT_SIZE_XS,
    color: COLORS.SURFACE,
    fontWeight: '600',
  },
  errorContainer: {
    padding: SIZES.SPACING_MD,
    backgroundColor: `${COLORS.ERROR}15`,
    borderRadius: SIZES.BORDER_RADIUS_MD,
    marginBottom: SIZES.SPACING_MD,
  },
  errorText: {
    color: COLORS.ERROR,
    fontSize: SIZES.FONT_SIZE_SM,
    textAlign: 'center',
  },
  loadingContainer: {
    padding: SIZES.SPACING_XL,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
  },
  emptyContainer: {
    padding: SIZES.SPACING_XL,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: SIZES.FONT_SIZE_MD,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  bottomSpacing: {
    height: SIZES.SPACING_XL,
  },
});

export default TemplateManagementScreen;
